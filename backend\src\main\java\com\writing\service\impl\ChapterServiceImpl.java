package com.writing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.writing.entity.Chapter;
import com.writing.mapper.ChapterMapper;
import com.writing.service.ChapterService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 章节服务实现类
 */
@Service
@RequiredArgsConstructor
public class ChapterServiceImpl extends ServiceImpl<ChapterMapper, Chapter> implements ChapterService {
    
    @Override
    public List<Chapter> getChaptersByNovelId(Long novelId) {
        return this.list(new LambdaQueryWrapper<Chapter>()
                .eq(Chapter::getNovelId, novelId)
                .orderByAsc(Chapter::getChapterOrder));
    }
    
    @Override
    public Chapter createChapter(Long novelId, Chapter chapter) {
        chapter.setNovelId(novelId);
        
        // 设置章节顺序
        if (chapter.getChapterOrder() == null) {
            Integer maxOrder = getMaxChapterOrder(novelId);
            chapter.setChapterOrder(maxOrder + 1);
        }
        
        // 计算字数
        if (chapter.getContent() != null) {
            chapter.setWordCount(calculateWordCount(chapter.getContent()));
        }
        
        this.save(chapter);
        return chapter;
    }
    
    @Override
    public Chapter updateChapter(Long novelId, Chapter chapter) {
        Chapter existingChapter = getChapterDetail(novelId, chapter.getId());
        if (existingChapter == null) {
            throw new RuntimeException("章节不存在");
        }
        
        chapter.setNovelId(novelId);
        
        // 重新计算字数
        if (chapter.getContent() != null) {
            chapter.setWordCount(calculateWordCount(chapter.getContent()));
        }
        
        this.updateById(chapter);
        return chapter;
    }
    
    @Override
    public boolean deleteChapter(Long novelId, Long chapterId) {
        Chapter chapter = getChapterDetail(novelId, chapterId);
        if (chapter == null) {
            throw new RuntimeException("章节不存在");
        }
        
        return this.removeById(chapterId);
    }
    
    @Override
    public Chapter getChapterDetail(Long novelId, Long chapterId) {
        return this.getOne(new LambdaQueryWrapper<Chapter>()
                .eq(Chapter::getId, chapterId)
                .eq(Chapter::getNovelId, novelId));
    }
    
    @Override
    public void updateChapterOrder(Long novelId, List<Long> chapterIds) {
        for (int i = 0; i < chapterIds.size(); i++) {
            Chapter chapter = new Chapter();
            chapter.setId(chapterIds.get(i));
            chapter.setChapterOrder(i + 1);
            this.updateById(chapter);
        }
    }
    
    @Override
    public int calculateWordCount(String content) {
        if (content == null || content.trim().isEmpty()) {
            return 0;
        }
        
        // 移除HTML标签和空白字符，计算实际字数
        String cleanContent = content.replaceAll("<[^>]*>", "")
                .replaceAll("\\s+", "");
        
        return cleanContent.length();
    }
    
    /**
     * 获取小说中章节的最大顺序号
     */
    private Integer getMaxChapterOrder(Long novelId) {
        List<Chapter> chapters = this.list(new LambdaQueryWrapper<Chapter>()
                .eq(Chapter::getNovelId, novelId)
                .orderByDesc(Chapter::getChapterOrder)
                .last("LIMIT 1"));
        
        if (chapters.isEmpty()) {
            return 0;
        }
        
        Integer maxOrder = chapters.get(0).getChapterOrder();
        return maxOrder != null ? maxOrder : 0;
    }
}
