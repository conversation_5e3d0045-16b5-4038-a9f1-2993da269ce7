-- 创建数据库
CREATE DATABASE IF NOT EXISTS writing_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE writing_db;

-- 用户表
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 小说表
CREATE TABLE `novels` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '小说ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '小说标题',
  `description` text COMMENT '小说描述',
  `genre` varchar(50) DEFAULT NULL COMMENT '小说类型',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
  `status` varchar(20) DEFAULT 'writing' COMMENT '状态：writing-写作中，completed-已完成，paused-暂停',
  `word_count` int DEFAULT '0' COMMENT '总字数',
  `chapter_count` int DEFAULT '0' COMMENT '章节数',
  `tags` json DEFAULT NULL COMMENT '标签',
  `outline` text COMMENT '大纲',
  `genre_prompt` text COMMENT '类型提示词',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_genre` (`genre`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_novels_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小说表';

-- 章节表
CREATE TABLE `chapters` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '章节ID',
  `novel_id` bigint NOT NULL COMMENT '小说ID',
  `title` varchar(200) NOT NULL COMMENT '章节标题',
  `content` longtext COMMENT '章节内容',
  `summary` text COMMENT '章节摘要',
  `outline` text COMMENT '章节大纲',
  `notes` text COMMENT '章节备注',
  `status` varchar(20) DEFAULT 'draft' COMMENT '状态：planned-计划中，draft-草稿，completed-已完成',
  `word_count` int DEFAULT '0' COMMENT '字数',
  `chapter_order` int NOT NULL COMMENT '章节顺序',
  `ai_generated` tinyint DEFAULT '0' COMMENT '是否AI生成：0-否，1-是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_novel_id` (`novel_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_chapters_novel_id` FOREIGN KEY (`novel_id`) REFERENCES `novels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='章节表';

-- 人物表
CREATE TABLE `characters` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '人物ID',
  `novel_id` bigint NOT NULL COMMENT '小说ID',
  `name` varchar(100) NOT NULL COMMENT '人物姓名',
  `role` varchar(20) DEFAULT 'supporting' COMMENT '角色类型：protagonist-主角，supporting-配角，antagonist-反派',
  `gender` varchar(10) DEFAULT 'male' COMMENT '性别：male-男，female-女，other-其他',
  `age` int DEFAULT NULL COMMENT '年龄',
  `appearance` text COMMENT '外貌描述',
  `personality` text COMMENT '性格描述',
  `background` text COMMENT '背景故事',
  `tags` json DEFAULT NULL COMMENT '标签',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `generated` tinyint DEFAULT '0' COMMENT '是否AI生成：0-否，1-是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_novel_id` (`novel_id`),
  KEY `idx_role` (`role`),
  CONSTRAINT `fk_characters_novel_id` FOREIGN KEY (`novel_id`) REFERENCES `novels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人物表';

-- 世界观设定表
CREATE TABLE `world_settings` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设定ID',
  `novel_id` bigint NOT NULL COMMENT '小说ID',
  `title` varchar(200) NOT NULL COMMENT '设定标题',
  `description` text COMMENT '设定描述',
  `category` varchar(50) DEFAULT 'setting' COMMENT '分类：setting-设定，location-地点，system-体系，rule-规则',
  `details` text COMMENT '详细内容',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_novel_id` (`novel_id`),
  KEY `idx_category` (`category`),
  CONSTRAINT `fk_world_settings_novel_id` FOREIGN KEY (`novel_id`) REFERENCES `novels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='世界观设定表';

-- 语料库表
CREATE TABLE `corpus` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '语料ID',
  `novel_id` bigint NOT NULL COMMENT '小说ID',
  `title` varchar(200) NOT NULL COMMENT '语料标题',
  `type` varchar(50) DEFAULT 'description' COMMENT '类型：description-描述，dialogue-对话，scene-场景',
  `content` text NOT NULL COMMENT '语料内容',
  `tags` json DEFAULT NULL COMMENT '标签',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_novel_id` (`novel_id`),
  KEY `idx_type` (`type`),
  CONSTRAINT `fk_corpus_novel_id` FOREIGN KEY (`novel_id`) REFERENCES `novels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语料库表';

-- 事件表
CREATE TABLE `events` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `novel_id` bigint NOT NULL COMMENT '小说ID',
  `title` varchar(200) NOT NULL COMMENT '事件标题',
  `description` text COMMENT '事件描述',
  `chapter` varchar(100) DEFAULT NULL COMMENT '所属章节',
  `time` varchar(100) DEFAULT NULL COMMENT '事件时间',
  `importance` varchar(20) DEFAULT 'normal' COMMENT '重要性：low-低，normal-普通，high-高',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_novel_id` (`novel_id`),
  KEY `idx_importance` (`importance`),
  CONSTRAINT `fk_events_novel_id` FOREIGN KEY (`novel_id`) REFERENCES `novels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事件表';

-- 提示词表
CREATE TABLE `prompts` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '提示词ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '提示词标题',
  `category` varchar(50) NOT NULL COMMENT '分类：outline-大纲，content-内容，character-人物，worldview-世界观',
  `description` text COMMENT '描述',
  `content` text NOT NULL COMMENT '提示词内容',
  `tags` json DEFAULT NULL COMMENT '标签',
  `is_default` tinyint DEFAULT '0' COMMENT '是否默认：0-否，1-是',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_category` (`category`),
  CONSTRAINT `fk_prompts_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词表';

-- 写作目标表
CREATE TABLE `writing_goals` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '目标ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '目标标题',
  `type` varchar(20) NOT NULL COMMENT '类型：daily-每日，weekly-每周，monthly-每月，custom-自定义',
  `target_value` int NOT NULL COMMENT '目标数值',
  `current_value` int DEFAULT '0' COMMENT '当前进度',
  `unit` varchar(20) DEFAULT 'words' COMMENT '单位：words-字数，chapters-章节，hours-小时',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态：active-进行中，completed-已完成，paused-暂停',
  `priority` int DEFAULT '0' COMMENT '优先级',
  `progress_history` json DEFAULT NULL COMMENT '进度历史',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_writing_goals_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='写作目标表';

-- 小说类型表
CREATE TABLE `novel_genres` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `code` varchar(50) NOT NULL COMMENT '类型代码',
  `name` varchar(100) NOT NULL COMMENT '类型名称',
  `description` text COMMENT '类型描述',
  `color` varchar(20) DEFAULT NULL COMMENT '颜色',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `prompt` text COMMENT '类型提示词',
  `is_default` tinyint DEFAULT '0' COMMENT '是否默认：0-否，1-是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_code` (`code`),
  CONSTRAINT `fk_novel_genres_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小说类型表';

-- API配置表
CREATE TABLE `api_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `type` varchar(20) NOT NULL COMMENT '类型：official-官方，custom-自定义',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `base_url` varchar(255) NOT NULL COMMENT '基础URL',
  `selected_model` varchar(100) DEFAULT NULL COMMENT '选择的模型',
  `max_tokens` int DEFAULT '2000000' COMMENT '最大Token数',
  `unlimited_tokens` tinyint DEFAULT '0' COMMENT '无限Token：0-否，1-是',
  `temperature` decimal(3,2) DEFAULT '0.70' COMMENT '温度参数',
  `top_p` decimal(3,2) DEFAULT '1.00' COMMENT 'Top P参数',
  `frequency_penalty` decimal(3,2) DEFAULT '0.00' COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) DEFAULT '0.00' COMMENT '存在惩罚',
  `timeout` int DEFAULT '30' COMMENT '超时时间（秒）',
  `stream_mode` tinyint DEFAULT '1' COMMENT '流模式：0-否，1-是',
  `retry_count` int DEFAULT '3' COMMENT '重试次数',
  `custom_headers` text COMMENT '自定义请求头',
  `status` varchar(20) DEFAULT 'disconnected' COMMENT '状态：connected-已连接，disconnected-未连接',
  `is_active` tinyint DEFAULT '0' COMMENT '是否激活：0-否，1-是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  CONSTRAINT `fk_api_configs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API配置表';

-- 账户余额表
CREATE TABLE `account_balance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '余额ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `balance` decimal(10,4) DEFAULT '0.0000' COMMENT '账户余额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_account_balance_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户余额表';

-- 计费记录表
CREATE TABLE `billing_records` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `model` varchar(100) NOT NULL COMMENT '使用的模型',
  `input_tokens` int DEFAULT '0' COMMENT '输入Token数',
  `output_tokens` int DEFAULT '0' COMMENT '输出Token数',
  `cost` decimal(10,4) NOT NULL COMMENT '费用',
  `request_type` varchar(50) DEFAULT NULL COMMENT '请求类型',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_model` (`model`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_billing_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='计费记录表';

-- Token使用统计表
CREATE TABLE `token_usage_stats` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `total_input_tokens` bigint DEFAULT '0' COMMENT '总输入Token数',
  `total_output_tokens` bigint DEFAULT '0' COMMENT '总输出Token数',
  `total_cost` decimal(10,4) DEFAULT '0.0000' COMMENT '总费用',
  `last_reset_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '最后重置日期',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_token_usage_stats_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Token使用统计表';

-- 写作记录表
CREATE TABLE `writing_records` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `novel_id` bigint NOT NULL COMMENT '小说ID',
  `date` date NOT NULL COMMENT '写作日期',
  `words_written` int DEFAULT '0' COMMENT '当日写作字数',
  `time_spent` int DEFAULT '0' COMMENT '写作时长（分钟）',
  `chapters_completed` int DEFAULT '0' COMMENT '完成章节数',
  `notes` text COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_novel_id` (`novel_id`),
  KEY `idx_date` (`date`),
  CONSTRAINT `fk_writing_records_novel_id` FOREIGN KEY (`novel_id`) REFERENCES `novels` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='写作记录表';

-- 公告阅读记录表
CREATE TABLE `announcement_reads` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `last_read_version` varchar(50) DEFAULT NULL COMMENT '最后阅读版本',
  `last_read_date` datetime DEFAULT NULL COMMENT '最后阅读时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  CONSTRAINT `fk_announcement_reads_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告阅读记录表';
