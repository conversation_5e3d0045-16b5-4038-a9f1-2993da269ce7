package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Chapter;

import java.util.List;

/**
 * 章节服务接口
 */
public interface ChapterService extends IService<Chapter> {
    
    /**
     * 获取小说的章节列表
     */
    List<Chapter> getChaptersByNovelId(Long novelId);
    
    /**
     * 创建章节
     */
    Chapter createChapter(Long novelId, Chapter chapter);
    
    /**
     * 更新章节
     */
    Chapter updateChapter(Long novelId, Chapter chapter);
    
    /**
     * 删除章节
     */
    boolean deleteChapter(Long novelId, Long chapterId);
    
    /**
     * 获取章节详情
     */
    Chapter getChapterDetail(Long novelId, Long chapterId);
    
    /**
     * 更新章节顺序
     */
    void updateChapterOrder(Long novelId, List<Long> chapterIds);
    
    /**
     * 计算章节字数
     */
    int calculateWordCount(String content);
}
